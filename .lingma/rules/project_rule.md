# 项目代码规范

## 代码规范

- 代码风格尽量和当前类或者同一包下的其他类的代码风格保持一致
- 生成代码后, 需要确保新写的代码中所用到的对象都正确import, 必须不能有编译性错误, 如果有编译错误请自动修复
- 代码生成后, 需确保改动到的文件, 都没有编译错误
- 代码生成后, 当前类如有未被使用的 import 对象, 需要自动清理
- 如果选中的代码块中包好箭头注释 // ->, 请优先从这个箭头注释的位置添加新的代码, 代码生成后需要删除箭头注释
- 集合或字符串相关的操作, 需要使用apache的工具类, 如 CollectionUtils StringUtils ListUtils MapUtils ObjectUtils等
- 尽量使用Optional优化null判断和从对象读取某个属性值
- 如果在原有的方法上新增代码, 新增代码如果超过20行, 需要单独提取一个方法出来提高代码可读性
- 涉及到外部接口的调用, 当方法返回的对象是 `ResponVO` 时, 需要按下面的样例抛出 `BizException`

```java
ResponseVO<UserVO> userResponse = userApi.get(userId);
if (!userResponse.isSuccess()) {
    throw new BizException(userResponse.getError().getCode(), userResponse.getError().getDesc());
}
```

- 对于新增的属性, 必须有中文注释
- Controller接口禁止使用 @RequestMapping, 必须使用指定请求方法的如 @GetMapping @PostMapping 等等
- 接口参数校验规则, 使用 javax.validation.constraints 包下的注解实现参数校验, 如 @NotBlank @NotNull @Size 等, 代码例子如下:
```java
@Data
public class VideoUploadDTO {
    @NotBlank(message = "视频标题不能为空")
    private String title;
    
    @NotNull(message = "视频分类不能为空")
    private Integer categoryId;
    
    @Size(max = 500, message = "描述不能超过500字")
    private String description;
}
```

- 对集合的操作, 优先使用java8的stream操作
- 新建的实体类, 必须使用 `lombok` 注解代替 get set 方法


## 日志规范

- 如果当前类或父类中没有实例化Logger日志对象, 优先在类上使用 Lombok 的 `@Slf4j` 注解
- 日志中如需打印对象类型的数据, 需转为json字符串打印, 其中json序列化工具使用 `JacksonUtil` 类
- 必须使用英文日志, 禁止使用中文日志

```java
@Slf4j
public class VideoServiceImpl {
    public void processVideo(VideoDTO video) {
        log.info("start process video, req: {}", JacksonUtil.writeAsString(video));
        try {
            // 业务处理
        } catch (Exception e) {
            log.error("process video fail, videoId: {}, exception msg: ", video.getId(), e);
            throw new BizException(ErrorCode.VIDEO_PROCESS_ERROR);
        }
    }
}
```