---
description: 
globs: 
alwaysApply: false
---
# Java单元测试代码生成规范

## 单元测试规范

单元测试遵循以下规范：

1. **测试框架使用**:
   - 使用Mockito进行mock测试

2. **测试文件存放**:
   - 单元测试代码放在`src/test/java`目录下
   - 单元测试类与被测试类保持一致的包路径和命名风格

3. **测试方法命名**:
   - 统一使用 `test_` 开头, 接着是被测试的方法名, 然后是测试用例简述, 最后是期望的测试结果, 全部单元测试必须严格遵循这种命名规范
   - 测试命名示例:
     ```java
      @Test
      public void test_spFinishTaskIfSuccess_whenTaskStatusIsProcessing_returnFalse() {

      }
     ```
    - 如果一个用例已经有对应的单元测试方法, 请在原有的单元测试方法上新增或者修改代码测试代码

4. **测试方法结构**:
   - 每个测试方法需包含准备（Arrange）、执行（Act）和断言（Assert）三个部分
   - 测试方法需要包含多个断言，确保逻辑分支的完整性
   - 使用`@Test`注解标记测试方法
   - 使用`@Before`进行测试准备工作

5. **测试注释**:
   - 每个测试方法需包含以下注释信息：
     - 测试场景说明
     - 测试用例详细描述
     - 期望结果说明

6. **测试覆盖率要求**:
   - 单元测试必须要覆盖目标方法全部的逻辑分支, 每种情况都需要设计用例写一个单元测试
   - 确保所有代码路径和边界条件都被测试

7. **JSON处理规范**:
   - 优先使用对象模型而不是直接使用JSON字符串或Map
   - 使用`JacksonUtil`进行对象与JSON的转换
   - 确保对象字段名与JSON字段名一致

8. **公共mock数据初始化**
    - 请不要在 `@Before` 上初始化太多mock数据, 跟业务相关的mock数据要放在具体的单元测试用例上mock数据

9. **配置值注入处理**:
    - 对于被测试类中使用`@Value`注入的配置值，必须在测试类的`@BeforeEach`方法中使用`ReflectionTestUtils.setField`进行显式赋值
    - 配置值应该使用与生产环境相同或合理的测试值
    - 示例：
      ```java
      @BeforeEach
      public void setUp() {
          ReflectionTestUtils.setField(targetService, "configField", "testValue");
      }
      ```

10. **私有方法测试规范**:
   - 对于需要测试的私有方法，使用反射机制进行调用
   - 反射调用示例:
     ```java
     Method privateMethod = TargetClass.class.getDeclaredMethod("methodName", parameterTypes);
     privateMethod.setAccessible(true);
     privateMethod.invoke(targetInstance, parameters);
     ```
   - 处理反射调用的异常时，需要从 InvocationTargetException 中提取实际的业务异常
   - 私有方法测试应该集中在关键的业务逻辑上，避免测试简单的工具方法


