#!/usr/bin/env python3
import os
import subprocess
import sys
from datetime import datetime
import re
import ast
import javalang
from typing import List, Dict, Any

def run_git_command(cmd, cwd=None):
    try:
        result = subprocess.run(cmd, cwd=cwd, shell=True, capture_output=True, text=True)
        return result.stdout.strip()
    except Exception as e:
        print(f"执行Git命令时出错: {e}")
        return ""

def find_git_projects(base_dir):
    projects = []
    for root, dirs, files in os.walk(base_dir):
        if '.git' in dirs:
            projects.append(root)
            dirs.remove('.git')  # 不再递归进入.git目录
        if 'node_modules' in dirs:
            dirs.remove('node_modules')  # 不递归进入node_modules
    return projects

def get_commit_details(project_path, commit_hash):
    details = {
        'hash': commit_hash,
        'message': run_git_command(f'git show -s --format=%B {commit_hash}', project_path),
        'author': run_git_command(f'git show -s --format=%an {commit_hash}', project_path),
        'date': run_git_command(f'git show -s --format=%cd --date=format:"%Y-%m-%d %H:%M:%S" {commit_hash}', project_path),
        'files': []
    }
    
    files_output = run_git_command(f'git show --name-only --pretty=format:"" {commit_hash}', project_path)
    if files_output:
        details['files'] = [f for f in files_output.split('\n') if f and f.endswith('.java')]
    
    return details

def get_file_content_at_commit(project_path, commit_hash, file_path):
    """获取指定提交时的文件完整内容"""
    return run_git_command(f'git show {commit_hash}:{file_path}', project_path)

def generate_test_data(param_type: str) -> str:
    """根据参数类型生成测试数据"""
    type_mapping = {
        'String': '"test"',
        'Integer': '1',
        'Long': '1L',
        'Boolean': 'true',
        'Double': '1.0',
        'Float': '1.0f',
        'List': 'new ArrayList<>()',
        'Set': 'new HashSet<>()',
        'Map': 'new HashMap<>()',
        'Date': 'new Date()',
        'LocalDateTime': 'LocalDateTime.now()',
        'LocalDate': 'LocalDate.now()',
        'BigDecimal': 'new BigDecimal("1.0")'
    }
    return type_mapping.get(param_type, 'null')

def parse_java_file(content: str) -> Dict[str, Any]:
    """解析Java文件内容，返回类和方法信息"""
    try:
        tree = javalang.parse.parse(content)
        result = {
            'package': None,
            'imports': [],
            'class_name': None,
            'methods': [],
            'constructors': []
        }
        
        # 获取包名
        if tree.package:
            result['package'] = str(tree.package.name)
            
        # 获取导入
        for imp in tree.imports:
            result['imports'].append(str(imp.path))
            
        # 获取类信息
        for path, node in tree.filter(javalang.tree.ClassDeclaration):
            result['class_name'] = node.name
            
            # 获取构造函数信息
            for constructor in node.constructors:
                constructor_info = {
                    'name': node.name,
                    'parameters': [],
                    'throws': [str(t) for t in constructor.throws] if constructor.throws else []
                }
                
                # 获取参数信息
                for param in constructor.parameters:
                    param_info = {
                        'type': str(param.type),
                        'name': param.name
                    }
                    constructor_info['parameters'].append(param_info)
                    
                result['constructors'].append(constructor_info)
            
            # 获取方法信息
            for method in node.methods:
                method_info = {
                    'name': method.name,
                    'modifiers': list(method.modifiers),
                    'return_type': str(method.return_type) if method.return_type else 'void',
                    'parameters': [],
                    'throws': [str(t) for t in method.throws] if method.throws else []
                }
                
                # 获取参数信息
                for param in method.parameters:
                    param_info = {
                        'type': str(param.type),
                        'name': param.name
                    }
                    method_info['parameters'].append(param_info)
                    
                result['methods'].append(method_info)
                
        return result
    except Exception as e:
        print(f"解析Java文件时出错: {e}")
        return None

def generate_test_method(method_info: Dict[str, Any], class_name: str) -> str:
    """生成测试方法代码"""
    method_name = method_info['name']
    is_private = 'private' in method_info['modifiers']
    is_static = 'static' in method_info['modifiers']
    return_type = method_info['return_type']
    
    test_method = []
    # 生成测试方法注释
    test_method.append(f"    /**")
    test_method.append(f"     * Test for {method_name}")
    test_method.append(f"     */")
    
    # 生成测试方法注解
    test_method.append(f"    @Test")
    
    # 生成测试方法签名
    if is_private:
        test_method.append(f"    public void testPrivate{method_name}_WhenNormalCase() throws Exception {{")
    else:
        test_method.append(f"    public void test{method_name}_WhenNormalCase() {{")
    
    # 生成测试方法内容
    test_method.append(f"        // given")
    
    # 生成参数准备代码
    for param in method_info['parameters']:
        param_type = param['type']
        param_name = param['name']
        test_data = generate_test_data(param_type.split('<')[0])  # 处理泛型
        test_method.append(f"        {param_type} {param_name} = {test_data};")
    
    test_method.append(f"")
    test_method.append(f"        // when")
    
    # 生成方法调用代码
    params_str = ", ".join(p['name'] for p in method_info['parameters'])
    if is_private:
        test_method.append(f"        Method method = {class_name}.class.getDeclaredMethod(\"{method_name}\"{', ' + ', '.join(p['type'] + '.class' for p in method_info['parameters']) if method_info['parameters'] else ''});")
        test_method.append(f"        method.setAccessible(true);")
        if is_static:
            test_method.append(f"        {return_type} result = ({return_type}) method.invoke(null{', ' + params_str if params_str else ''});")
        else:
            test_method.append(f"        {return_type} result = ({return_type}) method.invoke(instance{', ' + params_str if params_str else ''});")
    else:
        if is_static:
            test_method.append(f"        {return_type} result = {class_name}.{method_name}({params_str});")
        else:
            test_method.append(f"        {return_type} result = instance.{method_name}({params_str});")
    
    test_method.append(f"")
    test_method.append(f"        // then")
    if return_type != 'void':
        test_method.append(f"        assertThat(result).isNotNull(); // TODO: Add proper assertions")
    
    test_method.append(f"    }}")
    test_method.append(f"")
    
    return "\n".join(test_method)

def generate_test_class(java_info: Dict[str, Any], original_file_path: str) -> str:
    """生成测试类代码"""
    test_class = []
    
    # 生成包声明
    if java_info['package']:
        test_class.append(f"package {java_info['package']};")
        test_class.append("")
    
    # 生成导入语句
    test_class.extend([
        "import org.junit.jupiter.api.BeforeEach;",
        "import org.junit.jupiter.api.Test;",
        "import org.mockito.Mock;",
        "import org.mockito.MockitoAnnotations;",
        "import static org.assertj.core.api.Assertions.assertThat;",
        "import static org.mockito.Mockito.*;",
        "import java.lang.reflect.Method;",
        "import java.util.*;",
        "import java.time.*;",
        "import java.math.BigDecimal;",
        ""
    ])
    
    # 添加原类的导入
    for imp in java_info['imports']:
        test_class.append(f"import {imp};")
    
    test_class.append("")
    
    # 生成类注释
    test_class.append(f"/**")
    test_class.append(f" * Test for {java_info['class_name']}")
    test_class.append(f" */")
    
    # 生成类定义
    test_class.append(f"public class {java_info['class_name']}Test {{")
    test_class.append("")
    
    # 生成被测试类实例
    test_class.append(f"    private {java_info['class_name']} instance;")
    test_class.append("")
    
    # 生成setUp方法
    test_class.append(f"    @BeforeEach")
    test_class.append(f"    public void setUp() {{")
    test_class.append(f"        MockitoAnnotations.openMocks(this);")
    
    # 如果有构造函数，使用第一个构造函数创建实例
    if java_info['constructors']:
        constructor = java_info['constructors'][0]
        params = []
        for param in constructor['parameters']:
            test_data = generate_test_data(param['type'].split('<')[0])
            params.append(test_data)
        params_str = ", ".join(params)
        test_class.append(f"        instance = new {java_info['class_name']}({params_str});")
    else:
        test_class.append(f"        instance = new {java_info['class_name']}();")
    
    test_class.append(f"    }}")
    test_class.append("")
    
    # 生成测试方法
    for method in java_info['methods']:
        test_class.append(generate_test_method(method, java_info['class_name']))
    
    test_class.append("}")
    
    return "\n".join(test_class)

def create_test_file(project_path: str, original_file: str, test_content: str):
    """创建测试文件"""
    # 将src/main/java替换为src/test/java
    test_file = original_file.replace('src/main/java', 'src/test/java')
    # 确保目标目录存在
    os.makedirs(os.path.dirname(os.path.join(project_path, test_file)), exist_ok=True)
    # 写入测试文件
    with open(os.path.join(project_path, test_file), 'w', encoding='utf-8') as f:
        f.write(test_content)
    return test_file

def main(ticket_id):
    base_dir = '/Users/<USER>/Code/polyv'
    
    # 查找所有git项目
    projects = find_git_projects(base_dir)
    if not projects:
        print('未找到任何git项目，请检查目录配置。')
        return
    
    processed_projects = 0
    total_projects = len(projects)
    generated_tests = []
    
    # 遍历每个项目
    for project_path in projects:
        processed_projects += 1
        project_name = os.path.basename(project_path)
        print(f'[{processed_projects}/{total_projects}] 正在搜索项目: {project_name}')
        
        # 查找包含指定ticket_id的提交
        commits = run_git_command(f'git log --all --pretty=format:"%H" --grep="{ticket_id}"', project_path).split('\n')
        commits = [c for c in commits if c]
        
        if not commits:
            print('  - 未找到相关提交')
            continue
        
        print(f'  - 找到 {len(commits)} 个相关提交')
        
        # 处理每个提交
        for commit in commits:
            commit_details = get_commit_details(project_path, commit)
            
            # 只处理Java源代码文件
            java_files = commit_details['files']
            
            print(f'  - 提交 {commit[:8]} 修改了 {len(java_files)} 个Java源代码文件')
            
            for file in java_files:
                # 获取文件内容
                content = get_file_content_at_commit(project_path, commit, file)
                if not content:
                    continue
                
                # 解析Java文件
                java_info = parse_java_file(content)
                if not java_info or not java_info['methods']:
                    continue
                
                # 生成测试类代码
                test_content = generate_test_class(java_info, file)
                
                # 创建测试文件
                test_file = create_test_file(project_path, file, test_content)
                
                generated_tests.append({
                    'project': project_name,
                    'original_file': file,
                    'test_file': test_file,
                    'class_name': java_info['class_name'],
                    'methods': [m['name'] for m in java_info['methods']]
                })
                
                print(f'    - 已生成测试文件: {test_file}')
    
    if not generated_tests:
        print(f'未找到与 {ticket_id} 相关的Java代码提交记录。')
        return
    
    # 生成测试生成报告
    report = f'# 单元测试生成报告: {ticket_id}\n\n'
    report += '## 概述\n\n'
    report += f'- 生成时间: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}\n'
    report += f'- 扫描项目: {total_projects} 个\n'
    report += f'- 涉及项目: {", ".join(set(t["project"] for t in generated_tests))}\n'
    report += f'- 生成测试类: {len(generated_tests)} 个\n\n'
    
    # 输出详细的测试生成信息
    report += '## 测试类详情\n\n'
    
    for test in generated_tests:
        report += f'### 项目: {test["project"]}\n\n'
        report += f'- 原始文件: {test["original_file"]}\n'
        report += f'- 测试文件: {test["test_file"]}\n'
        report += f'- 测试类名: {test["class_name"]}Test\n'
        report += f'- 测试方法:\n'
        for method in test['methods']:
            report += f'  - test{method}_WhenNormalCase\n'
        report += '\n'
    
    # 保存报告
    report_file = os.path.join(os.getcwd(), f'unit_test_{ticket_id}.md')
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write(report)
    
    print(f'\n单元测试生成报告已生成: {report_file}')

if __name__ == '__main__':
    if len(sys.argv) < 2:
        print('请提供需求单号，例如: python unit_test.py PQYO-1001')
        sys.exit(1)
    
    main(sys.argv[1]) 