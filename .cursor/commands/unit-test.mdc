---
description: 
globs: 
alwaysApply: false
---
# 单元测试生成命令

该命令用于查找与特定需求单号相关的所有git提交，并通过AI为修改的Java代码生成单元测试。

## 用法

```bash
/unit-test <需求单号>
```

例如：
```bash
/unit-test PQYO-1001
```

## 功能说明

1. 查找代码变更
   - 根据需求单号查找相关的git提交
   - 识别提交中修改的Java文件和方法

2. 分析代码结构
   - 分析类的结构和依赖
   - 识别方法的参数和返回类型
   - 识别私有方法和静态方法

3. 生成单元测试
   - 遵循 unit-test-rules.mdc 中定义的测试规范
   - 生成测试类和测试方法
   - 包含正常流程、异常流程和边界条件测试
   - 处理私有方法和静态方法的测试场景

## 实现流程

1. 用户输入需求单号
2. 命令查找相关的git提交和代码变更
3. 将代码变更信息发送给AI
4. AI根据单元测试规范生成测试代码
5. 将生成的测试代码写入对应的test目录

## AI提示模板

```
请为以下Java代码生成单元测试：

[代码内容]

要求：
1. 遵循项目的单元测试规范
2. 包含正常流程、异常流程和边界条件测试
3. 使用Mockito处理外部依赖
4. 使用AssertJ进行断言
5. 私有方法使用反射测试
6. 为每个测试方法添加清晰的注释
```

## 输出示例

AI会生成如下格式的测试代码：

```java
package com.example;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.*;

/**
 * Test for ExampleService
 */
public class ExampleServiceTest {
    
    private ExampleService instance;
    
    @Mock
    private DependencyService dependencyService;
    
    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
        instance = new ExampleService(dependencyService);
    }
    
    /**
     * Test normal case for process method
     */
    @Test
    public void testProcess_WhenInputValid() {
        // given
        String input = "test";
        when(dependencyService.validate(input)).thenReturn(true);
        
        // when
        String result = instance.process(input);
        
        // then
        assertThat(result).isNotNull()
                         .isEqualTo("processed:test");
        verify(dependencyService).validate(input);
    }
}
```

## 注意事项

1. 生成的测试代码需要人工审查和调整
2. 复杂的业务逻辑可能需要补充更多测试场景
3. 需要确保测试数据的合理性和完整性
4. 可能需要补充必要的mock配置
