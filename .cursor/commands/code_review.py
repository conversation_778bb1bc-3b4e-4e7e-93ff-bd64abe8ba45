#!/usr/bin/env python3
import os
import subprocess
import sys
from datetime import datetime
import re

def run_git_command(cmd, cwd=None):
    try:
        result = subprocess.run(cmd, cwd=cwd, shell=True, capture_output=True, text=True)
        return result.stdout.strip()
    except Exception as e:
        print(f"执行Git命令时出错: {e}")
        return ""

def find_git_projects(base_dir):
    projects = []
    for root, dirs, files in os.walk(base_dir):
        if '.git' in dirs:
            projects.append(root)
            dirs.remove('.git')  # 不再递归进入.git目录
        if 'node_modules' in dirs:
            dirs.remove('node_modules')  # 不递归进入node_modules
    return projects

def get_commit_details(project_path, commit_hash):
    details = {
        'hash': commit_hash,
        'message': run_git_command(f'git show -s --format=%B {commit_hash}', project_path),
        'author': run_git_command(f'git show -s --format=%an {commit_hash}', project_path),
        'date': run_git_command(f'git show -s --format=%cd --date=format:"%Y-%m-%d %H:%M:%S" {commit_hash}', project_path),
        'files': []
    }
    
    files_output = run_git_command(f'git show --name-only --pretty=format:"" {commit_hash}', project_path)
    if files_output:
        details['files'] = [f for f in files_output.split('\n') if f and not f.endswith('pom.xml')]
    
    return details

def get_file_content_at_commit(project_path, commit_hash, file_path):
    """获取指定提交时的文件完整内容"""
    return run_git_command(f'git show {commit_hash}:{file_path}', project_path)

def extract_java_method(file_content, method_name):
    """提取Java文件中的完整方法内容"""
    # 使用正则表达式匹配方法定义到方法结束
    method_pattern = re.compile(
        r'(?:(?:public|private|protected|static|final|native|synchronized|abstract|transient)+\s+)*' # 修饰符
        rf'[\w\<\>\[\]]+\s+{method_name}\s*\([^\)]*\)\s*(?:throws\s+[\w\s,]+)?\s*\{{' # 方法签名
        r'(?:[^{}]|\{(?:[^{}]|\{[^{}]*\})*\})*' # 方法体
        r'\}', re.MULTILINE)
    
    match = method_pattern.search(file_content)
    return match.group(0) if match else None

def is_import_change(diff_line):
    """判断是否是import语句的改动"""
    return diff_line.strip().startswith(('+import ', '-import '))

def get_changed_methods(project_path, commit_hash, file_path):
    """获取改动的方法信息"""
    diff = run_git_command(f'git show {commit_hash} -- "{file_path}"', project_path)
    methods = []
    
    # 获取文件在当前提交的完整内容
    file_content = get_file_content_at_commit(project_path, commit_hash, file_path)
    if not file_content:
        return []

    # 使用正则表达式匹配方法名
    method_name_pattern = re.compile(r'(?:public|private|protected)\s+(?:static\s+)?[\w\<\>\[\]]+\s+(\w+)\s*\(')
    
    # 分析diff内容，找出改动的行所在的方法
    current_method = None
    for line in diff.split('\n'):
        if line.startswith(('+', '-')) and not is_import_change(line):
            # 尝试从改动行中提取方法名
            match = method_name_pattern.search(line)
            if match:
                method_name = match.group(1)
                method_content = extract_java_method(file_content, method_name)
                if method_content and method_name not in [m['name'] for m in methods]:
                    methods.append({
                        'name': method_name,
                        'content': method_content,
                        'diff': line
                    })
            elif current_method:
                # 如果当前行属于某个方法内的改动
                current_method['diff'] += '\n' + line

    return methods

def main(ticket_id):
    base_dir = '/Users/<USER>/Code/polyv'
    
    # 查找所有git项目
    projects = find_git_projects(base_dir)
    if not projects:
        print('未找到任何git项目，请检查目录配置。')
        return
    
    all_changes = []
    processed_projects = 0
    total_projects = len(projects)
    
    # 遍历每个项目
    for project_path in projects:
        processed_projects += 1
        project_name = os.path.basename(project_path)
        print(f'[{processed_projects}/{total_projects}] 正在搜索项目: {project_name}')
        
        # 查找包含指定ticket_id的提交
        commits = run_git_command(f'git log --all --pretty=format:"%H" --grep="{ticket_id}"', project_path).split('\n')
        commits = [c for c in commits if c]
        
        if not commits:
            print('  - 未找到相关提交')
            continue
        
        print(f'  - 找到 {len(commits)} 个相关提交')
        
        # 处理每个提交
        for commit in commits:
            commit_details = get_commit_details(project_path, commit)
            
            # 只处理Java源代码文件
            source_files = [f for f in commit_details['files'] if f.endswith('.java')]
            
            print(f'  - 提交 {commit[:8]} 修改了 {len(source_files)} 个Java源代码文件')
            
            for file in source_files:
                changed_methods = get_changed_methods(project_path, commit, file)
                if changed_methods:
                    all_changes.append({
                        'project': project_name,
                        'project_path': project_path,
                        'commit': commit_details,
                        'file': file,
                        'methods': changed_methods
                    })
    
    if not all_changes:
        print(f'未找到与 {ticket_id} 相关的Java代码提交记录。')
        return
    
    # 生成代码变更报告
    report = f'# 代码变更报告: {ticket_id}\n\n'
    report += '## 概述\n\n'
    report += f'- 收集时间: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}\n'
    report += f'- 扫描项目: {total_projects} 个\n'
    report += f'- 涉及项目: {", ".join(set(c["project"] for c in all_changes))}\n'
    report += f'- 文件数量: {len(all_changes)}\n\n'
    
    # 添加问题分析占位符
    report += '## 问题分析\n\n'
    report += '<claude_analysis>\n'
    report += '请分析代码中存在的潜在问题\n'
    report += '</claude_analysis>\n\n'
    
    # 输出详细的代码变更信息
    report += '## 代码变更详情\n\n'
    
    for change in all_changes:
        report += f'### 项目: {change["project"]}\n\n'
        report += f'#### 文件: {change["file"]}\n\n'
        report += f'- 提交: {change["commit"]["hash"][:8]}\n'
        report += f'- 作者: {change["commit"]["author"]}\n'
        report += f'- 时间: {change["commit"]["date"]}\n'
        report += f'- 提交信息: {change["commit"]["message"]}\n\n'
        
        for method in change['methods']:
            report += f'##### 方法: {method["name"]}\n\n'
            report += '改动内容:\n'
            report += '```diff\n' + method['diff'] + '\n```\n\n'
            report += '完整方法:\n'
            report += '```java\n' + method['content'] + '\n```\n\n'
            report += '<claude_method_analysis>\n'
            report += f'请分析 {method["name"]} 方法中的潜在问题\n'
            report += '</claude_method_analysis>\n\n'
            report += '---\n\n'
    
    # 保存报告
    report_file = os.path.join(os.getcwd(), f'code_review_{ticket_id}.md')
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write(report)
    
    print(f'\n代码变更报告已生成: {report_file}')

if __name__ == '__main__':
    if len(sys.argv) < 2:
        print('请提供需求单号，例如: python code_review.py PQYO-10001')
        sys.exit(1)
    
    main(sys.argv[1]) 