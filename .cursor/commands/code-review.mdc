---
description: 
globs: 
alwaysApply: false
---
---
title: code-review
description: 根据需求单号审查相关代码提交
author: Claude
version: 0.1.0
date: 2023-11-15
command:
  usage: /code-review <需求单号>
  example: /code-review PQYO-10001
---

# 代码审查工具

该命令用于查找与特定需求单号相关的所有git提交，并对修改的代码进行审查。

## 用法

```
/code-review <需求单号>
```

例如：
```
/code-review PQYO-10001
```

## 实现

该命令使用Python实现，主要文件为`code_review.py`。工具会扫描指定目录下的所有git项目，查找与需求单号相关的提交，对涉及到修改的代码所在的方法进行全面的代码审查。

### 主要功能

1. 扫描git项目
   - 递归扫描指定目录下的所有git项目
   - 自动跳过node_modules等无关目录

2. 查找相关提交
   - 使用git log命令查找包含需求单号的提交
   - 提取提交的详细信息（作者、时间、消息等）

3. 分析代码变更
   - 支持多种编程语言（Java、Python、JavaScript等）
   - 检查代码质量和潜在问题
   - 按严重程度分类问题（严重、高危、中等、提示）

4. 生成审查报告
   - 生成结构化的Markdown格式报告
   - 包含完整的代码变更记录
   - 提供详细的审查意见

### 审查范围

支持以下类型的文件：
- Java (.java)
- Kotlin (.kt)
- XML (.xml)
- JavaScript/TypeScript (.js, .ts, .jsx, .tsx)
- Python (.py)
- Go (.go)
- C/C++ (.c, .cpp, .h, .hpp)
- Ruby (.rb)
- PHP (.php)
- Scala (.scala)
- Rust (.rs)
- Swift (.swift)

### 代码分析规则

代码审查的首要任务是发现所有潜在的bug和问题。审查人员必须以发现bug为最高优先级，对代码进行全面细致的检查。

需要重点检查的bug类型：

1. 严重级别问题（最高优先级）
   - 并发安全问题（如竞态条件、死锁风险）
   - 代码逻辑问题
   - 数据一致性问题（如事务边界、状态不一致）
   - 内存泄漏和资源泄露
   - 空指针和类型转换异常
   - 业务逻辑错误
   - 安全漏洞

2. 高危级别问题
   - 异常处理不当
   - 参数校验不完整
   - 边界条件处理
   - 性能隐患
   - 数据库操作风险

3. 中等级别问题
   - 代码冗余
   - 配置项规范
   - 日志记录不完整
   - 注释缺失

4. 提示级别问题
   - 代码格式
   - 命名规范
   - 文档完整性

对于发现的每个bug，审查报告必须包含：
- 问题的详细描述
- 可能的影响范围
- 复现条件
- 修复建议
- 优先级评估

## 注意事项

1. 该命令仅审查代码变更，不会修改任何代码
2. 审查结果仅供参考，最终决策仍需开发人员判断
3. 大型项目的审查可能需要一些时间完成
4. 建议在代码提交前进行自查，减少潜在问题

## 配置要求

1. 系统要求：
   - Python 3.6+
   - Git 2.0+

2. 目录配置：
   - 确保.cursor/commands/code_review.py具有执行权限
   - 配置正确的项目扫描路径

## 输出说明

工具会生成一个名为`code_review_<需求单号>.md`的报告文件，包含：

1. 概述信息
   - 审查时间
   - 扫描项目数量
   - 涉及项目列表
   - 文件数量

2. Bug分析（重点内容）
   - 按严重程度分类的bug列表
   - 每个bug的详细说明
   - 修复建议

3. 代码质量分析
   - 代码结构评估
   - 性能影响分析
   - 可维护性评估

## 最佳实践

1. 重点关注并发、事务、异常处理等容易出现bug的场景
2. 使用检查清单确保不遗漏重要的检查点
3. 对每个发现的bug进行影响评估
4. 提供具体的修复建议
