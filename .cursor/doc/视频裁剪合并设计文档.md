# 视频裁剪合并设计文档

## 1. 概述

本文档详细描述了直播平台中视频裁剪合并功能的完整技术实现方案。该系统采用分布式架构设计，通过多个模块协作完成视频的裁剪和合并处理。

## 2. 系统架构

```mermaid
graph TB
    A[用户提交裁剪任务] --> B[live-webapp-aggregate<br/>接口层]
    B --> C[数据库队列<br/>ClipFileQueue]
    C --> D[live-ext-executor<br/>定时任务模块]
    D --> E[Docker容器调度]
    E --> F[mp4-process-docker<br/>视频处理容器]
    F --> G[live-api-cloud<br/>回调接口]
    G --> H[完成通知]
    
    style B fill:#e1f5fe
    style D fill:#fff3e0
    style F fill:#f3e5f5
    style G fill:#e8f5e8
```

## 3. 核心流程详解

### 3.1 第一阶段：任务提交 (live-webapp-aggregate)

#### 3.1.1 核心接口
- **路径**: `POST /v2/channel/record/mp4/batch-clip.do`
- **控制器**: `ChannelRecordFilesController.java`
- **方法**: `batchMp4Clip()`

#### 3.1.2 请求参数模型
```java
// 文件位置: src/main/java/com/live/req/ChannelRecordFileBatchClipReq.java
public class ChannelRecordFileBatchClipReq {
    private String fileId;                    // 暂存文件ID
    private Integer channelId;                // 频道ID
    private List<ClipSegment> clipSegmentList; // 裁剪片段列表(最多20个)
    
    public static class ClipSegment {
        private String timeFrame;             // 时间片段
        private String title;                 // 标题
        private Long cataId;                  // 目录ID
        private String autoConvert;           // 是否自动转存
    }
}
```

#### 3.1.3 核心业务逻辑
```java
// 位置: ChannelRecordFilesController.java:589-625
public ResponseObject batchMp4Clip(@Validated @RequestBody ChannelRecordFileBatchClipReq batchClipReq, HttpServletRequest request) {
    // 1. 参数验证
    String fileId = batchClipReq.getFileId();
    Integer channelId = batchClipReq.getChannelId();
    List<ClipSegment> clipSegmentList = batchClipReq.getClipSegmentList();
    
    // 2. 文件存在性验证
    RecordFile recordFile = recordFileManager.getByFileId(fileId);
    if (null == recordFile || !Objects.equals(channelId, recordFile.getChannelId())) {
        return fail(ResponseCode.RECORD_FILE_NOT_EXIST);
    }
    
    // 3. 限制检查 (最多20个片段)
    if (clipSegmentList.size() > 20) {
        return fail(ResponseCode.CHANNEL_RECORD_FILE_CLIP_SEGMENT_OVER_LIMIT);
    }
    
    // 4. 批量添加到裁剪队列
    for (ClipSegment clipSegment : clipSegmentList) {
        channelRecordFileService.addClipMp4Queue(recordFile, timeFrame, title, ip, autoConvert, cataId, null);
    }
    
    return success();
}
```

#### 3.1.4 队列添加服务
- **服务类**: `ChannelRecordFileService`
- **方法**: `addClipMp4Queue()`
- **功能**: 将裁剪任务添加到 `ClipFileQueue` 数据库表中

### 3.2 第二阶段：任务调度 (live-ext-executor)

#### 3.2.1 核心组件
- **Docker任务处理器**: `DockerTaskHandler.java`
- **启动入口**: `ExtApplication.java`

#### 3.2.2 Docker容器调度
```java
// 位置: DockerTaskHandler.java:120-134
private void dockerClipTaskExce() {
    ClipFileQueueStatus status = ClipFileQueueStatus.WAIT_DOCKER_CLIP;
    final ClipFileQueue clipFileQueue = clipFileQueueManager.queryLastQueueByStatus(status, offset);
    
    if (Objects.isNull(clipFileQueue)) {
        return;
    }
    
    // 启动Docker裁剪容器
    boolean result = dockerMp4ClipService.startDockerClipWithLock(clipFileQueue);
}
```

#### 3.2.3 任务状态管理
```java
// 任务状态枚举: ClipFileQueueStatus
public enum ClipFileQueueStatus {
    WAIT_CLIP(0, "等待裁剪"),
    CLIPING(1, "裁剪中"),
    WAIT_DOCKER_CLIP(2, "等待Docker裁剪"),
    FINISH(3, "裁剪完成"),
    CLIP_ERROR(4, "裁剪失败"),
    PULL_ERROR(5, "拉取失败"),
    // ... 其他状态
}
```

### 3.3 第三阶段：视频处理 (mp4-process-docker)

#### 3.3.1 容器架构
```
mp4-process-docker/
├── src/main/java/net/polyv/micro/modules/mp4/docker/
│   ├── Mp4DockerApplication.java          # 应用入口
│   ├── service/
│   │   ├── TaskService.java               # 任务基础服务
│   │   ├── ClipTaskService.java           # 裁剪任务服务
│   │   ├── ClipTaskChainService.java      # 裁剪任务链
│   │   ├── ClipMp4BoxService.java         # MP4Box裁剪实现
│   │   ├── ClipFfmpegService.java         # FFmpeg裁剪实现
│   │   ├── DownloadTaskChainService.java  # 下载服务
│   │   └── OssTaskChainService.java       # OSS上传服务
│   └── util/
│       └── FfmpegUtil.java                # FFmpeg工具类
```

#### 3.3.2 任务处理链
```java
// 位置: ClipTaskService.java:28-35
@Override
protected List<TaskChainService> buildTaskChains(){
    List<TaskChainService> chains = Lists.newArrayList();
    chains.add(downloadService);    // 1. 下载原视频
    chains.add(clipService);        // 2. 执行裁剪
    chains.add(ossService);         // 3. 上传结果
    return chains;
}
```

#### 3.3.3 裁剪算法实现
```java
// 位置: ClipTaskChainService.java:38-85
@Override
public void doTask(TaskReqDTO taskReq, TaskDTO taskRsp) {
    List<TimeFrameDTO> clipTimeFrames = taskReq.getClipTimes();
    String file = taskRsp.getFiles().stream().findFirst().get();

    // 1. 获取视频时长
    int duration = (int) (analysisVideoDuration(file) * 1000);
    
    // 2. 计算保留的时间段
    List<TimeFrameDTO> saveTimeFrames = convertToSaveTimeFrame(clipTimeFrames, duration, isProductExplainToMaterialClip);
    
    // 3. 选择裁剪工具 (MP4Box优先，失败则切换FFmpeg)
    Mp4ToolEnum toolType = Mp4ToolEnum.getInstance(taskReq.getToolType());
    
    if (Objects.equals(Mp4ToolEnum.MP4BOX, toolType)){
        try {
            clipMp4BoxService.clip(taskReq, taskRsp, isProductExplainToMaterialClip);
        } catch (Exception e) {
            clipFfmpegService.clip(taskReq, taskRsp, isProductExplainToMaterialClip);
        }
    } else {
        try {
            clipFfmpegService.clip(taskReq, taskRsp, isProductExplainToMaterialClip);
        } catch (Exception e) {
            clipMp4BoxService.clip(taskReq, taskRsp, isProductExplainToMaterialClip);
        }
    }
    
    // 4. 验证裁剪结果
    long targetDuration = analysisVideoDuration(targetFile);
    if (targetDuration <= 0){
        throw new BizException(Mp4DockerErrorCodeEnum.COMBINE_FAIL.getCode(), "裁剪后视频时长为0");
    }
}
```

#### 3.3.4 FFmpeg裁剪命令
```java
// 位置: FfmpegUtil.java:51-56
private static final String FFMPEG_CLIP = "%s -i %s -ss %s -c copy -to %s %s";
private static final String FFMPEG_ENCODE_CLIP = "%s -ss %s -i %s -to %s -c copy -y %s";

public static boolean clipVideo(String ffmpeg, int clipTimeStart, int clipTimeEnd, String clipPath, String targetPath) {
    String command = String.format(FFMPEG_CLIP, ffmpeg, clipPath, 
        convertMsToSeconds(clipTimeStart), convertMsToSeconds(clipTimeEnd), targetPath);
    
    int status = CommandUtils.execCommand(command);
    return status == 0;
}
```

### 3.4 第四阶段：结果回调 (live-api-cloud)

#### 3.4.1 回调接口
- **路径**: `POST /internal/v3/docker/mp4/clip/callback`
- **控制器**: `DockerMp4InternalController.java`
- **方法**: `clipCallback()`

#### 3.4.2 回调处理逻辑
```java
// 位置: DockerMp4InternalController.java:83-95
@PostMapping("/clip/callback")
public ResponseEntity<WrappedResponse> clipCallback(@RequestBody DockerMp4TaskRspDto params) {
    
    logger.info("docker clip callback params: {}", JacksonUtil.writeAsString(params));

    try{
        dockerMp4ClipService.finishDockerClip(params);
    }catch(Exception e){
        logger.error("clipCallback Exception", e);
    }
    
    return success();
}
```

#### 3.4.3 回调参数模型
```java
public class DockerMp4TaskRspDto {
    private boolean success;        // 处理是否成功
    private String taskId;          // 任务ID
    private String outputUrl;       // 输出文件URL
    private Long duration;          // 视频时长
    private Long fileSize;          // 文件大小
    private Integer errorCode;      // 错误码
    private String errorMsg;        // 错误信息
}
```

## 4. 数据库设计

### 4.1 裁剪队列表 (ClipFileQueue)
```sql
CREATE TABLE `clip_file_queue` (
  `id` int NOT NULL AUTO_INCREMENT,
  `file_id` varchar(64) NOT NULL COMMENT '文件ID',
  `channel_id` int NOT NULL COMMENT '频道ID',
  `user_id` varchar(64) NOT NULL COMMENT '用户ID',
  `clip_file_id` varchar(64) NOT NULL COMMENT '裁剪后文件ID',
  `time_frame` text NOT NULL COMMENT '裁剪时间段JSON',
  `file_name` varchar(255) NOT NULL COMMENT '文件名称',
  `status` tinyint NOT NULL DEFAULT '0' COMMENT '状态',
  `callback_url` varchar(512) COMMENT '回调URL',
  `source` tinyint DEFAULT '1' COMMENT '来源',
  `created_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `last_modified` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_status_time` (`status`, `created_time`),
  KEY `idx_file_id` (`file_id`),
  KEY `idx_channel_id` (`channel_id`)
) COMMENT='裁剪队列表';
```

### 4.2 合并裁剪队列表 (CombineClipQueue)
```sql
CREATE TABLE `combine_clip_queue` (
  `id` int NOT NULL AUTO_INCREMENT,
  `channel_id` int NOT NULL COMMENT '频道ID',
  `user_id` varchar(64) NOT NULL COMMENT '用户ID',
  `file_ids` text NOT NULL COMMENT '文件ID列表',
  `session_id` varchar(64) COMMENT '会话ID',
  `status` tinyint NOT NULL DEFAULT '0' COMMENT '状态',
  `origin` tinyint DEFAULT '1' COMMENT '来源',
  `created_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `last_modified` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_status_time` (`status`, `created_time`)
) COMMENT='合并裁剪队列表';
```

## 5. 配置管理

### 5.1 live-ext-executor 配置
```yaml
# application.yml
ext:
  clip:
    mp4:
      pool:
        size: 10          # 裁剪线程池大小
      axel:
        size: 30          # 下载并发数
  clipDir: /data/clip/    # 裁剪临时目录
```

### 5.2 mp4-process-docker 配置
```yaml
# application.yml
polyv:
  common:
    tmpDataPath: /data/temp    # 临时文件目录

ffmpeg:
  path: /usr/local/bin/ffmpeg  # FFmpeg可执行文件路径
mp4box:
  path: /usr/local/bin/MP4Box  # MP4Box可执行文件路径
```

## 6. 错误处理与监控

### 6.1 异常分类
```java
public enum ClipFileQueueStatus {
    CLIP_ERROR(4, "裁剪失败"),
    PULL_ERROR(5, "拉取失败"),
    GET_DURATION_ERROR(6, "获取时长失败"),
    UPLOAD_ERROR(7, "上传失败"),
    ADD_RECORD_ERROR(8, "添加记录失败")
}
```

### 6.2 重试机制
- **超时检查**: 每5分钟检查一次超时任务(默认18000秒)
- **失败重试**: MP4Box失败自动切换FFmpeg
- **状态恢复**: 异常任务自动重置状态

### 6.3 监控指标
- 队列任务数量
- 处理成功率
- 平均处理时长
- 错误分布统计

## 7. 性能优化

### 7.1 并发处理
- **多线程队列处理**: 默认10个线程并发处理裁剪任务
- **Docker容器池**: 动态创建和销毁容器实例
- **分布式锁**: Redis防止重复处理同一任务

### 7.2 存储优化
- **临时文件清理**: 处理完成后5秒延迟清理临时文件
- **分片存储**: 按日期分目录存储临时文件
- **CDN加速**: 支持多CDN源下载和上传

## 8. 扩展性设计

### 8.1 插件化架构
- **任务链模式**: 下载 → 裁剪 → 上传的可扩展链条
- **工具切换**: MP4Box和FFmpeg双工具支持
- **格式扩展**: 支持新的视频格式处理

### 8.2 水平扩展
- **多实例部署**: live-ext-executor支持多实例部署
- **容器编排**: Kubernetes管理mp4-process-docker容器
- **负载均衡**: 任务分发到不同处理节点

## 9. 安全考虑

### 9.1 输入验证
- 文件格式白名单
- 时间段合法性检查
- 文件大小限制

### 9.2 权限控制
- 用户权限验证
- 频道归属检查
- API访问限制

## 10. 总结

本视频裁剪合并系统采用了分布式微服务架构，通过队列异步处理、容器化部署、多工具备选等技术手段，实现了高可用、高性能的视频处理能力。系统具备良好的扩展性和容错性，能够满足大规模直播平台的业务需求。

### 核心优势
1. **高可靠性**: 多重错误处理和重试机制
2. **高性能**: 并发处理和容器化部署
3. **易扩展**: 插件化架构和水平扩展能力
4. **易维护**: 清晰的模块划分和统一的接口设计 