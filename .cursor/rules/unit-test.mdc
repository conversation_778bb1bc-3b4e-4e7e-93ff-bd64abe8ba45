---
description: 
globs: 
alwaysApply: false
---
请对选中的方法生成单元测试, 单元测试规范如下:

# 单元测试代码生成规范
- 单元测试框架使用 Mockito 和 PowerMockito, 优先使用Mockito, 如遇到私有方法, 静态方法或者final方法时, 使用PowerMockito
- 单元测试代码放在src/test/java下, 单元测试的类名 包名 和单元测试方法名称参考被测试的方法
- 单元测试必须覆盖目标方法90%以上的逻辑分支, 其中核心逻辑分支必须要覆盖到
- 单元测试代码生成后, 必须确保无编译错误, 必须确保可以执行通过
- 单元测试代码生成后, 自动执行当前测试类的全部单元测试方法
- 针对每个单元测试方法, 需要在方法注释中写清楚测试场景 场景用例 以及期望的结果

# 单元测试中JSON字段赋值规范

## 1. 基本原则

1. 优先使用对象模型而不是直接使用json字符串或者Map
2. 保持代码的可读性和可维护性
3. 确保类型安全
4. 遵循面向对象的设计原则

## 2. 处理流程

```mermaid
graph TD
    A[开始] --> B{是否有对应的BO对象?}
    B -->|是| C[使用BO对象创建实例]
    B -->|否| D[使用Map创建]
    C --> E[设置对象属性]
    D --> F[使用put方法设置属性]
    E --> G[使用JacksonUtil转换为JSON]
    F --> G
    G --> H[设置到目标字段]
    H --> I[结束]
```

## 3. 具体规则

### 3.1 使用BO对象的情况

```java
// 1. 导入必要的类
import net.polyv.modules.channel.api.bo.ai.AIPPTVideoExtBO;
import net.polyv.modules.channel.api.bo.ai.VideoProduceDraftBO;
import net.polyv.modules.common.util.JacksonUtil;

// 2. 创建BO对象实例
AIPPTVideoExtBO ext = new AIPPTVideoExtBO();
VideoProduceDraftBO draft = new VideoProduceDraftBO();

// 3. 设置属性
draft.setDraftJsonUrl("http://test.com");
draft.setExpire(-1);
ext.setDraft(draft);
ext.setExpireTime(LocalDateTime.now().plusDays(30));

// 4. 转换为JSON字符串
String extJson = JacksonUtil.writeAsString(ext);

// 5. 设置到目标对象
video.setExt(extJson);
```

### 3.2 使用Map的情况（仅在没有BO对象时使用）

```java
// 1. 创建Map对象
Map<String, Object> ext = new HashMap<>();
Map<String, Object> draft = new HashMap<>();

// 2. 设置属性
draft.put("draftJsonUrl", "http://test.com");
draft.put("expire", -1);
ext.put("draft", draft);
ext.put("expireTime", LocalDateTime.now().plusDays(30));

// 3. 转换为JSON字符串
String extJson = JacksonUtil.writeAsString(ext);

// 4. 设置到目标对象
video.setExt(extJson);
```

## 4. 最佳实践

1. 优先使用BO对象而不是Map
2. 保持属性名称的一致性
3. 使用有意义的变量名
4. 添加适当的注释说明
5. 确保日期时间格式的正确性
6. 处理可能的空值情况

## 5. 示例代码

```java
/**
 * 测试场景：设置视频的ext字段
 */
@Test
public void testSetVideoExt() {
    // 准备测试数据
    AiPptVideo video = new AiPptVideo();
    
    // 创建ext对象
    AIPPTVideoExtBO ext = new AIPPTVideoExtBO();
    VideoProduceDraftBO draft = new VideoProduceDraftBO();
    
    // 设置属性
    draft.setDraftJsonUrl("http://test.com");
    draft.setExpire(-1);
    ext.setDraft(draft);
    ext.setExpireTime(LocalDateTime.now().plusDays(30));
    
    // 转换为JSON并设置
    video.setExt(JacksonUtil.writeAsString(ext));
    
    // 验证结果
    assertNotNull("ext字段不应为空", video.getExt());
    // 其他断言...
}
```

## 6. 注意事项

1. 确保BO对象的属性名称与JSON字段名称一致
2. 注意日期时间格式的处理
3. 处理可能的空值情况
4. 保持代码的可读性和可维护性
5. 添加适当的注释说明