---
description: 
globs: 
alwaysApply: false
---
## 单元测试规范

单元测试遵循以下规范：

1. **测试框架使用**:
   - 优先使用Mockito进行mock测试
   - 对于私有方法、静态方法或final方法，使用PowerMockito

2. **测试文件存放**:
   - 单元测试代码放在`src/test/java`目录下
   - 单元测试类与被测试类保持一致的包路径和命名风格

3. **测试方法命名**:
   - `testXxx`或`shouldXxxWhenYyy`格式
4. **测试方法结构**:
   - 每个测试方法需包含准备（Arrange）、执行（Act）和断言（Assert）三个部分
   - 测试方法需要包含多个断言，确保逻辑分支的完整性
   - 使用`@Test`注解标记测试方法
   - 使用`@Before`进行测试准备工作

5. **测试注释**:
   - 每个测试方法需包含以下注释信息：
     - 测试场景说明
     - 测试用例详细描述
     - 期望结果说明

6. **测试覆盖率要求**:
   - 单元测试要覆盖目标方法90%以上的逻辑分支
   - 核心业务逻辑分支必须100%覆盖
   - 确保所有代码路径和边界条件都被测试

7. **JSON处理规范**:
   - 优先使用对象模型而不是直接使用JSON字符串或Map
   - 使用`JacksonUtil`进行对象与JSON的转换
   - 确保对象字段名与JSON字段名一致
