---
description: 
globs: 
alwaysApply: false
---
# VOD API Cloud开发规范

## 代码风格
- 遵循阿里巴巴Java开发手册规范
- 使用统一的代码格式化模板
- 类、方法、变量必须有清晰的中文注释
- 代码提交前必须进行格式化

## 日志规范
```java
// 使用Lombok的@Slf4j注解
@Slf4j
public class VideoServiceImpl {
    public void processVideo(VideoDTO video) {
        log.info("开始处理视频，参数：{}", JacksonUtil.toJsonString(video));
        try {
            // 业务处理
        } catch (Exception e) {
            log.error("处理视频失败，videoId={}，错误：", video.getId(), e);
            throw new BizException(ErrorCode.VIDEO_PROCESS_ERROR);
        }
    }
}
```



## 响应封装
```java
@PostMapping("/upload")
public ResponseVO<VideoVO> uploadVideo(@Valid @RequestBody VideoUploadDTO dto) {
    try {
        VideoVO video = videoService.upload(dto);
        return ResponseVO.success(video);
    } catch (BizException e) {
        return ResponseVO.error(e.getCode(), e.getMessage());
    }
}
```

## 事务管理
- 使用@Transactional注解管理事务
- 事务方法命名规范：以save/update/delete等开头
- 避免事务嵌套，使用REQUIRES_NEW处理独立事务

## 缓存使用
```java
@Cacheable(value = "video", key = "#id")
public VideoVO getVideo(Long id) {
    // 从数据库查询
}

@CacheEvict(value = "video", key = "#id")
public void updateVideo(Long id, VideoUpdateDTO dto) {
    // 更新操作
}
```

## 接口文档
```java
@Api(tags = "视频管理接口")
@RestController
@RequestMapping("/api/v1/videos")
public class VideoController {
    @ApiOperation("上传视频")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "title", value = "视频标题", required = true),
        @ApiImplicitParam(name = "categoryId", value = "分类ID", required = true)
    })
    @PostMapping("/upload")
    public ResponseVO<VideoVO> uploadVideo(@RequestBody VideoUploadDTO dto) {
        // 处理逻辑
    }
}
```

## 单元测试要求
- 核心业务逻辑必须编写单元测试
- 测试覆盖率要求达到80%以上
- 使用MockMvc测试Controller层
- 使用Mockito模拟外部依赖
